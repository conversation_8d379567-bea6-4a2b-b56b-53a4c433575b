2025-08-12 12:48:50,063 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 12:48:50,063 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 12:48:50,064 - app_android.config_android - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-08-12 12:48:50,064 - app_android.config_android - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_android
2025-08-12 12:48:50,065 - app_android.config_android - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_android
2025-08-12 12:48:50,065 - app_android.config_android - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-08-12 12:48:50,066 - app_android.config_android - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/android_data/test_suites
2025-08-12 12:48:50,066 - app_android.config_android - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_android/suites
2025-08-12 12:48:50,067 - app_android.config_android - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings_android
2025-08-12 12:48:50,067 - app_android.config_android - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_android
2025-08-12 12:48:50,068 - app_android.config_android - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/files_to_push
2025-08-12 12:48:50,068 - __main__ - INFO - Using instance-specific database paths with suffix: _port_8081
2025-08-12 12:48:50,068 - __main__ - INFO - Using custom ports (Flask: 8081, Appium: 4724, WDA: 8300) - preserving existing processes for multi-instance support
2025-08-12 12:48:50,068 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
2025-08-12 12:48:51,076 - app_android.utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values_port_8081.db
2025-08-12 12:48:51,077 - app_android.utils.global_values_db - INFO - Global values database initialized successfully
2025-08-12 12:48:51,078 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 12:48:51,078 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-08-12 12:48:51,079 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/android_data/reports
2025-08-12 12:48:51,079 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_ios
2025-08-12 12:48:51,079 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/android_data/reference_images
2025-08-12 12:48:51,080 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/android_data/test_suites
2025-08-12 12:48:51,080 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/suites
2025-08-12 12:48:51,081 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings_ios
2025-08-12 12:48:51,081 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_ios
2025-08-12 12:48:51,081 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-08-12 12:48:51,082 - app_android.utils.global_values_db - INFO - Using global values from config.py
2025-08-12 12:48:51,082 - app_android.utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-08-12 12:48:51,084 - app_android.utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-08-12 12:48:51,086 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-08-12 12:48:51,119 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-08-12 12:48:51,438 - app_android.utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 12:48:51,523 - utils.database - INFO - === UPDATING TEST_STEPS TABLE SCHEMA ===
2025-08-12 12:48:51,524 - utils.database - INFO - Test_steps table schema updated successfully
2025-08-12 12:48:51,524 - utils.database - INFO - === UPDATING SCREENSHOTS TABLE SCHEMA ===
2025-08-12 12:48:51,524 - utils.database - INFO - Screenshots table schema updated successfully
2025-08-12 12:48:51,524 - utils.database - INFO - === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
2025-08-12 12:48:51,525 - utils.database - INFO - step_idx column already exists in execution_tracking table
2025-08-12 12:48:51,525 - utils.database - INFO - action_type column already exists in execution_tracking table
2025-08-12 12:48:51,525 - utils.database - INFO - action_params column already exists in execution_tracking table
2025-08-12 12:48:51,525 - utils.database - INFO - action_id column already exists in execution_tracking table
2025-08-12 12:48:51,525 - utils.database - INFO - Successfully updated execution_tracking table schema
2025-08-12 12:48:51,526 - utils.database - INFO - Database initialized successfully
2025-08-12 12:48:51,526 - utils.database - INFO - Checking initial database state...
2025-08-12 12:48:51,529 - utils.database - INFO - Database state: 0 suites, 0 cases, 1000 steps, 0 screenshots, 1126 tracking entries
2025-08-12 12:48:51,549 - app - INFO - Using directories from config.py:
2025-08-12 12:48:51,549 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-08-12 12:48:51,549 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/android_data/reference_images
2025-08-12 12:48:51,550 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_ios
[2025-08-12 12:48:51,554] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-08-12 12:48:51,554] INFO in database: Test_steps table schema updated successfully
[2025-08-12 12:48:51,554] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-08-12 12:48:51,555] INFO in database: Screenshots table schema updated successfully
[2025-08-12 12:48:51,555] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-08-12 12:48:51,556] INFO in database: step_idx column already exists in execution_tracking table
[2025-08-12 12:48:51,556] INFO in database: action_type column already exists in execution_tracking table
[2025-08-12 12:48:51,557] INFO in database: action_params column already exists in execution_tracking table
[2025-08-12 12:48:51,557] INFO in database: action_id column already exists in execution_tracking table
[2025-08-12 12:48:51,557] INFO in database: Successfully updated execution_tracking table schema
[2025-08-12 12:48:51,557] INFO in database: Database initialized successfully
[2025-08-12 12:48:51,557] INFO in database: Checking initial database state...
[2025-08-12 12:48:51,558] INFO in database: Database state: 0 suites, 0 cases, 1000 steps, 0 screenshots, 1126 tracking entries
[2025-08-12 12:48:51,560] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-08-12 12:48:51,560] INFO in database: Test_steps table schema updated successfully
[2025-08-12 12:48:51,560] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-08-12 12:48:51,561] INFO in database: Screenshots table schema updated successfully
[2025-08-12 12:48:51,561] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-08-12 12:48:51,562] INFO in database: step_idx column already exists in execution_tracking table
[2025-08-12 12:48:51,562] INFO in database: action_type column already exists in execution_tracking table
[2025-08-12 12:48:51,562] INFO in database: action_params column already exists in execution_tracking table
[2025-08-12 12:48:51,562] INFO in database: action_id column already exists in execution_tracking table
[2025-08-12 12:48:51,562] INFO in database: Successfully updated execution_tracking table schema
[2025-08-12 12:48:51,562] INFO in database: Database initialized successfully
[2025-08-12 12:48:51,562] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-08-12 12:48:51,563] INFO in database: step_idx column already exists in execution_tracking table
[2025-08-12 12:48:51,563] INFO in database: action_type column already exists in execution_tracking table
[2025-08-12 12:48:51,563] INFO in database: action_params column already exists in execution_tracking table
[2025-08-12 12:48:51,563] INFO in database: action_id column already exists in execution_tracking table
[2025-08-12 12:48:51,563] INFO in database: Successfully updated execution_tracking table schema
[2025-08-12 12:48:51,563] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-08-12 12:48:51,564] INFO in database: Screenshots table schema updated successfully
[2025-08-12 12:48:51,639] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4724, WDA port: 8200
[2025-08-12 12:48:51,653] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4724): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x112a41400>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-08-12 12:48:51,653] INFO in appium_device_controller: Using custom ports (Appium: 4724, WDA: 8200) - preserving existing processes for multi-instance support
[2025-08-12 12:48:51,654] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[2025-08-12 12:48:51,654] INFO in appium_device_controller: Using local Appium installation at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium
[2025-08-12 12:48:52,541] INFO in appium_device_controller: Installed Appium drivers: 
[2025-08-12 12:48:52,541] INFO in appium_device_controller: Installing XCUITest driver...
[31mError: ✖ A driver named "xcuitest" is already installed. Did you mean to update? Run "appium driver update". See installed drivers with "appium driver list --installed".[39m
[2025-08-12 12:48:53,406] ERROR in appium_device_controller: Error checking/installing drivers: Command '['/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium', 'driver', 'install', 'xcuitest']' returned non-zero exit status 1.
[2025-08-12 12:48:53,406] INFO in appium_device_controller: Enabling inspector plugin if available
[2025-08-12 12:48:53,406] INFO in appium_device_controller: Appium server output will be logged to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/appium_server.log
[2025-08-12 12:48:53,414] INFO in appium_device_controller: Started Appium server using command: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium --base-path /wd/hub --port 4724 --relaxed-security --use-drivers xcuitest,uiautomator2 --use-plugins=inspector --session-override --allow-cors --allow-insecure chromedriver_autodownload
